#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V4增强系统评估器
评估集成红球大小比专项优化器后的整体性能
"""

import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

from v4_enhanced_integrator import V4EnhancedIntegrator

class V4EnhancedEvaluator:
    """V4增强系统评估器"""
    
    def __init__(self, data_path: str = "data/raw/dlt_data.csv"):
        self.data_path = Path(data_path)
        self.integrator = V4EnhancedIntegrator(data_path=str(data_path))
        self.df = None
        self.evaluation_results = {}
        
    def load_data(self):
        """加载数据"""
        print("[INFO] 加载评估数据...")
        
        self.df = pd.read_csv(self.data_path, header=None)
        self.df.columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2', '日期']
        
        # 数据类型转换
        numeric_cols = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        for col in numeric_cols:
            self.df[col] = pd.to_numeric(self.df[col], errors='coerce')
        
        self.df = self.df.dropna().reset_index(drop=True)
        
        # 计算实际比例
        red_balls = ['红球1', '红球2', '红球3', '红球4', '红球5']
        blue_balls = ['蓝球1', '蓝球2']
        
        self.df['实际红球奇数个数'] = self.df[red_balls].apply(lambda row: sum(x % 2 == 1 for x in row), axis=1)
        self.df['实际红球大数个数'] = self.df[red_balls].apply(lambda row: sum(x > 17 for x in row), axis=1)
        self.df['实际蓝球大数个数'] = self.df[blue_balls].apply(lambda row: sum(x > 6 for x in row), axis=1)
        
        print(f"[OK] 数据加载完成: {len(self.df)} 期")
        
    def evaluate_enhanced_system(self, test_periods: int = 50):
        """评估增强系统"""
        print(f"\n[STEP 1] 评估增强系统 (测试期数: {test_periods})")
        print("=" * 60)
        
        # 加载模型
        self.integrator.load_models()
        
        # 准备评估数据
        start_idx = len(self.df) - test_periods
        
        results = {
            '红球奇偶比': {'correct': 0, 'total': 0, 'predictions': [], 'actuals': []},
            '红球大小比': {'correct': 0, 'total': 0, 'predictions': [], 'actuals': []},
            '蓝球大小比': {'correct': 0, 'total': 0, 'predictions': [], 'actuals': []},
        }
        
        print("[INFO] 开始逐期预测评估...")
        
        for i in range(start_idx, len(self.df)):
            period = int(self.df.iloc[i]['期号'])
            
            # 使用历史数据进行预测
            historical_data = self.df.iloc[:i].copy()
            
            if len(historical_data) < 30:  # 确保有足够的历史数据
                continue
                
            try:
                # 执行预测
                pred_results = self.integrator.predict_enhanced(historical_data, period)
                
                # 获取实际值
                actual_red_odd = self.df.iloc[i]['实际红球奇数个数']
                actual_red_size = self.df.iloc[i]['实际红球大数个数']
                actual_blue_size = self.df.iloc[i]['实际蓝球大数个数']
                
                # 评估红球奇偶比
                if '红球奇偶比' in pred_results:
                    pred_red_odd = pred_results['红球奇偶比']
                    results['红球奇偶比']['predictions'].append(pred_red_odd)
                    results['红球奇偶比']['actuals'].append(actual_red_odd)
                    results['红球奇偶比']['total'] += 1
                    if pred_red_odd == actual_red_odd:
                        results['红球奇偶比']['correct'] += 1
                
                # 评估红球大小比
                if '红球大小比' in pred_results:
                    pred_red_size = pred_results['红球大小比']
                    results['红球大小比']['predictions'].append(pred_red_size)
                    results['红球大小比']['actuals'].append(actual_red_size)
                    results['红球大小比']['total'] += 1
                    if pred_red_size == actual_red_size:
                        results['红球大小比']['correct'] += 1
                
                # 评估蓝球大小比
                if '蓝球大小比' in pred_results:
                    pred_blue_size = pred_results['蓝球大小比']
                    results['蓝球大小比']['predictions'].append(pred_blue_size)
                    results['蓝球大小比']['actuals'].append(actual_blue_size)
                    results['蓝球大小比']['total'] += 1
                    if pred_blue_size == actual_blue_size:
                        results['蓝球大小比']['correct'] += 1
                
                if i % 10 == 0:
                    print(f"  已评估期号: {period}")
                    
            except Exception as e:
                print(f"[WARN] 期号 {period} 预测失败: {e}")
                continue
        
        # 计算准确率
        accuracies = {}
        for task, data in results.items():
            if data['total'] > 0:
                accuracy = data['correct'] / data['total']
                accuracies[task] = accuracy
                print(f"{task}准确率: {accuracy:.4f} ({data['correct']}/{data['total']})")
            else:
                accuracies[task] = 0.0
                print(f"{task}准确率: 无数据")
        
        self.evaluation_results = {
            'accuracies': accuracies,
            'detailed_results': results,
            'test_periods': test_periods
        }
        
        return accuracies
    
    def compare_with_baseline(self):
        """与基线系统对比"""
        print(f"\n[STEP 2] 与基线系统对比")
        print("=" * 60)
        
        # 基线性能（来自之前的评估）
        baseline_accuracies = {
            '红球奇偶比': 0.36,  # V4.0最终版
            '红球大小比': 0.22,  # V4.0最终版
            '蓝球大小比': 0.70   # V4.0最终版
        }
        
        enhanced_accuracies = self.evaluation_results['accuracies']
        
        print("性能对比:")
        print(f"{'任务':<12} {'基线准确率':<12} {'增强准确率':<12} {'提升幅度':<12}")
        print("-" * 50)
        
        improvements = {}
        for task in baseline_accuracies.keys():
            baseline = baseline_accuracies[task]
            enhanced = enhanced_accuracies.get(task, 0.0)
            improvement = enhanced - baseline
            improvements[task] = improvement
            
            print(f"{task:<12} {baseline:<12.4f} {enhanced:<12.4f} {improvement:+.4f}")
        
        # 总体评估
        avg_baseline = np.mean(list(baseline_accuracies.values()))
        avg_enhanced = np.mean([enhanced_accuracies.get(task, 0.0) for task in baseline_accuracies.keys()])
        overall_improvement = avg_enhanced - avg_baseline
        
        print(f"\n总体平均准确率:")
        print(f"  基线系统: {avg_baseline:.4f}")
        print(f"  增强系统: {avg_enhanced:.4f}")
        print(f"  总体提升: {overall_improvement:+.4f}")
        
        self.evaluation_results['comparison'] = {
            'baseline': baseline_accuracies,
            'enhanced': enhanced_accuracies,
            'improvements': improvements,
            'overall_improvement': overall_improvement
        }
        
        return improvements
    
    def analyze_red_size_ratio_improvement(self):
        """分析红球大小比改进效果"""
        print(f"\n[STEP 3] 红球大小比专项分析")
        print("=" * 60)
        
        if '红球大小比' not in self.evaluation_results['detailed_results']:
            print("[WARN] 没有红球大小比评估数据")
            return
        
        red_size_data = self.evaluation_results['detailed_results']['红球大小比']
        predictions = red_size_data['predictions']
        actuals = red_size_data['actuals']
        
        if not predictions:
            print("[WARN] 没有红球大小比预测数据")
            return
        
        # 混淆矩阵分析
        from collections import Counter
        
        print("预测分布:")
        pred_counter = Counter(predictions)
        actual_counter = Counter(actuals)
        
        for i in range(6):  # 0-5个大数
            pred_count = pred_counter.get(i, 0)
            actual_count = actual_counter.get(i, 0)
            print(f"  {i}个大数: 预测{pred_count}次, 实际{actual_count}次")
        
        # 准确率分析
        correct_by_class = {}
        total_by_class = {}
        
        for pred, actual in zip(predictions, actuals):
            if actual not in total_by_class:
                total_by_class[actual] = 0
                correct_by_class[actual] = 0
            
            total_by_class[actual] += 1
            if pred == actual:
                correct_by_class[actual] += 1
        
        print("\n各类别准确率:")
        for class_val in sorted(total_by_class.keys()):
            if total_by_class[class_val] > 0:
                accuracy = correct_by_class[class_val] / total_by_class[class_val]
                print(f"  {class_val}个大数: {accuracy:.4f} ({correct_by_class[class_val]}/{total_by_class[class_val]})")
        
        # 关键改进指标
        baseline_accuracy = 0.22
        enhanced_accuracy = self.evaluation_results['accuracies']['红球大小比']
        improvement_factor = enhanced_accuracy / baseline_accuracy if baseline_accuracy > 0 else float('inf')
        
        print(f"\n关键改进指标:")
        print(f"  基线准确率: {baseline_accuracy:.4f}")
        print(f"  增强准确率: {enhanced_accuracy:.4f}")
        print(f"  改进倍数: {improvement_factor:.2f}x")
        
        return {
            'baseline_accuracy': baseline_accuracy,
            'enhanced_accuracy': enhanced_accuracy,
            'improvement_factor': improvement_factor,
            'class_accuracies': {k: correct_by_class[k]/total_by_class[k] for k in total_by_class.keys()}
        }
    
    def generate_evaluation_report(self):
        """生成评估报告"""
        print(f"\n[STEP 4] 生成评估报告")
        print("=" * 60)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = Path(f"models/evaluation_reports/v4_enhanced_evaluation_{timestamp}.md")
        report_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# V4增强系统评估报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 总体性能
            f.write("## 总体性能\n\n")
            accuracies = self.evaluation_results['accuracies']
            for task, accuracy in accuracies.items():
                f.write(f"- {task}: {accuracy:.4f}\n")
            
            # 性能对比
            if 'comparison' in self.evaluation_results:
                f.write("\n## 性能对比\n\n")
                comparison = self.evaluation_results['comparison']
                f.write("| 任务 | 基线准确率 | 增强准确率 | 提升幅度 |\n")
                f.write("|------|------------|------------|----------|\n")
                
                for task in comparison['baseline'].keys():
                    baseline = comparison['baseline'][task]
                    enhanced = comparison['enhanced'].get(task, 0.0)
                    improvement = comparison['improvements'][task]
                    f.write(f"| {task} | {baseline:.4f} | {enhanced:.4f} | {improvement:+.4f} |\n")
                
                f.write(f"\n总体提升: {comparison['overall_improvement']:+.4f}\n")
            
            # 红球大小比专项分析
            f.write("\n## 红球大小比专项改进\n\n")
            f.write("这是本次优化的重点突破领域：\n\n")
            f.write("- 采用专门的集成学习方法\n")
            f.write("- 基于数据分析的特征工程\n")
            f.write("- 多模型投票机制\n")
            f.write("- 针对性的类别平衡处理\n")
        
        print(f"[OK] 评估报告已保存: {report_path}")
        return report_path
    
    def run_complete_evaluation(self, test_periods: int = 50):
        """运行完整评估"""
        print("V4增强系统评估器")
        print("=" * 70)
        
        # 加载数据
        self.load_data()
        
        # 评估增强系统
        accuracies = self.evaluate_enhanced_system(test_periods)
        
        # 与基线对比
        improvements = self.compare_with_baseline()
        
        # 红球大小比专项分析
        red_size_analysis = self.analyze_red_size_ratio_improvement()
        
        # 生成报告
        report_path = self.generate_evaluation_report()
        
        print(f"\n[SUCCESS] V4增强系统评估完成！")
        print(f"评估报告: {report_path}")
        
        return {
            'accuracies': accuracies,
            'improvements': improvements,
            'red_size_analysis': red_size_analysis,
            'report_path': report_path
        }

if __name__ == "__main__":
    evaluator = V4EnhancedEvaluator()
    results = evaluator.run_complete_evaluation()
