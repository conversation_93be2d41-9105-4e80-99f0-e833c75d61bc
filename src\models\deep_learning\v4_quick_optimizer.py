"""
V4.0 快速优化器
Quick Optimizer for V4.0 Transformer
专注于解决当前模型的核心问题
"""

import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple
import time
from datetime import datetime
import pickle

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..', '..')
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# TensorFlow导入
try:
    import tensorflow as tf
    from tensorflow import keras
    from sklearn.preprocessing import StandardScaler
    TF_AVAILABLE = True
except ImportError:
    print("[WARN] TensorFlow或sklearn不可用")
    TF_AVAILABLE = False


class V4QuickOptimizer:
    """V4.0 快速优化器"""
    
    def __init__(self):
        self.project_root = Path(project_root)
        self.data_path = self.project_root / "data" / "raw" / "dlt_data.csv"
        self.models_dir = self.project_root / "models" / "v4_trained"
        
    def load_and_prepare_data(self) -> pd.DataFrame:
        """加载并准备数据"""
        print("[INFO] 加载数据...")
        
        df = pd.read_csv(self.data_path, header=None)
        df.columns = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2', '日期']
        
        # 数据类型转换
        numeric_cols = ['期号', '红球1', '红球2', '红球3', '红球4', '红球5', '蓝球1', '蓝球2']
        for col in numeric_cols:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        df = df.dropna().reset_index(drop=True)
        
        # 基础特征工程
        red_balls = ['红球1', '红球2', '红球3', '红球4', '红球5']
        blue_balls = ['蓝球1', '蓝球2']
        
        # 基础比例特征
        df['红球奇数个数'] = df[red_balls].apply(lambda row: sum(x % 2 == 1 for x in row), axis=1)
        df['红球大数个数'] = df[red_balls].apply(lambda row: sum(x > 17 for x in row), axis=1)
        df['蓝球大数个数'] = df[blue_balls].apply(lambda row: sum(x > 6 for x in row), axis=1)
        
        # 统计特征
        df['红球和值'] = df[red_balls].sum(axis=1)
        df['红球均值'] = df[red_balls].mean(axis=1)
        df['红球方差'] = df[red_balls].var(axis=1)
        df['红球跨度'] = df[red_balls].max(axis=1) - df[red_balls].min(axis=1)
        
        df['蓝球和值'] = df[blue_balls].sum(axis=1)
        df['蓝球跨度'] = df[blue_balls].max(axis=1) - df[blue_balls].min(axis=1)
        
        # 区间分布特征
        for i, (start, end) in enumerate([(1, 7), (8, 14), (15, 21), (22, 28), (29, 35)]):
            df[f'红球区间{i+1}个数'] = df[red_balls].apply(
                lambda row: sum(start <= x <= end for x in row), axis=1
            )
        
        # 连号特征
        df['红球连号个数'] = df[red_balls].apply(self._count_consecutive, axis=1)
        df['蓝球连号个数'] = df[blue_balls].apply(self._count_consecutive, axis=1)
        
        # 历史趋势特征
        window_size = 5
        for col in ['红球奇数个数', '红球大数个数', '蓝球大数个数']:
            df[f'{col}_趋势'] = df[col].rolling(window=window_size, min_periods=1).mean()
            df[f'{col}_波动'] = df[col].rolling(window=window_size, min_periods=1).std().fillna(0)
        
        print(f"[OK] 数据准备完成: {len(df)} 期")
        return df
    
    def _count_consecutive(self, row) -> int:
        """计算连号个数"""
        sorted_nums = sorted(row)
        consecutive_count = 0
        for i in range(len(sorted_nums) - 1):
            if sorted_nums[i+1] - sorted_nums[i] == 1:
                consecutive_count += 1
        return consecutive_count
    
    def create_improved_model(self) -> keras.Model:
        """创建改进的模型"""
        if not TF_AVAILABLE:
            raise RuntimeError("TensorFlow不可用")
        
        # 输入层 (25个时间步, 22个特征)
        inputs = keras.Input(shape=(25, 22), name='sequence_input')
        
        # 改进的LSTM架构
        # 第一层LSTM - 增加单元数并使用双向
        x = keras.layers.Bidirectional(
            keras.layers.LSTM(128, return_sequences=True, dropout=0.3)
        )(inputs)
        
        # 第二层LSTM
        x = keras.layers.LSTM(96, return_sequences=False, dropout=0.3)(x)
        
        # 共享特征层 - 增加层数和复杂度
        shared = keras.layers.Dense(256, activation='relu')(x)
        shared = keras.layers.Dropout(0.4)(shared)
        shared = keras.layers.Dense(128, activation='relu')(shared)
        shared = keras.layers.Dropout(0.3)(shared)
        
        # 多任务输出头 - 针对性优化
        
        # 红球奇偶比 (0-5个奇数) - 保持原有结构
        red_odd_even = keras.layers.Dense(64, activation='relu')(shared)
        red_odd_even = keras.layers.Dense(6, activation='softmax', name='red_odd_even')(red_odd_even)
        
        # 红球大小比 (0-5个大数) - 重点优化这个任务
        red_size = keras.layers.Dense(128, activation='relu')(shared)  # 增加神经元
        red_size = keras.layers.Dropout(0.2)(red_size)
        red_size = keras.layers.Dense(64, activation='relu')(red_size)  # 增加一层
        red_size = keras.layers.Dense(6, activation='softmax', name='red_size')(red_size)
        
        # 蓝球大小比 (0-2个大数) - 保持良好表现
        blue_size = keras.layers.Dense(64, activation='relu')(shared)
        blue_size = keras.layers.Dense(3, activation='softmax', name='blue_size')(blue_size)
        
        # 红球号码预测 (35个号码)
        red_numbers = keras.layers.Dense(128, activation='relu')(shared)
        red_numbers = keras.layers.Dropout(0.3)(red_numbers)
        red_numbers = keras.layers.Dense(35, activation='sigmoid', name='red_numbers')(red_numbers)
        
        # 蓝球号码预测 (12个号码)
        blue_numbers = keras.layers.Dense(64, activation='relu')(shared)
        blue_numbers = keras.layers.Dense(12, activation='sigmoid', name='blue_numbers')(blue_numbers)
        
        # 创建模型
        model = keras.Model(
            inputs=inputs,
            outputs={
                'red_odd_even': red_odd_even,
                'red_size': red_size,
                'blue_size': blue_size,
                'red_numbers': red_numbers,
                'blue_numbers': blue_numbers
            },
            name='v4_improved_transformer'
        )
        
        # 编译模型 - 调整损失权重，重点优化红球大小比
        model.compile(
            optimizer=keras.optimizers.Adam(learning_rate=0.0005),  # 降低学习率
            loss={
                'red_odd_even': 'sparse_categorical_crossentropy',
                'red_size': 'sparse_categorical_crossentropy',
                'blue_size': 'sparse_categorical_crossentropy',
                'red_numbers': 'binary_crossentropy',
                'blue_numbers': 'binary_crossentropy'
            },
            loss_weights={
                'red_odd_even': 1.5,  # 适中权重
                'red_size': 4.0,      # 最高权重，重点优化
                'blue_size': 1.0,     # 保持现有表现
                'red_numbers': 1.0,   # 标准权重
                'blue_numbers': 1.0   # 标准权重
            },
            metrics={
                'red_odd_even': 'accuracy',
                'red_size': 'accuracy',
                'blue_size': 'accuracy',
                'red_numbers': 'binary_accuracy',
                'blue_numbers': 'binary_accuracy'
            }
        )
        
        return model
    
    def prepare_training_data(self, df: pd.DataFrame) -> Tuple[np.ndarray, Dict[str, np.ndarray], StandardScaler]:
        """准备训练数据"""
        sequences = []
        targets = {
            'red_odd_even': [],
            'red_size': [],
            'blue_size': [],
            'red_numbers': [],
            'blue_numbers': []
        }
        
        seq_len = 25
        
        # 选择特征列
        feature_cols = [
            '红球奇数个数', '红球大数个数', '蓝球大数个数',
            '红球和值', '红球均值', '红球方差', '红球跨度',
            '蓝球和值', '蓝球跨度',
            '红球区间1个数', '红球区间2个数', '红球区间3个数', '红球区间4个数', '红球区间5个数',
            '红球连号个数', '蓝球连号个数',
            '红球奇数个数_趋势', '红球大数个数_趋势', '蓝球大数个数_趋势',
            '红球奇数个数_波动', '红球大数个数_波动', '蓝球大数个数_波动'
        ]
        
        # 生成序列数据
        for i in range(seq_len, len(df)):
            # 输入序列特征
            sequence_features = []
            for j in range(i - seq_len, i):
                row_features = [df.iloc[j][col] for col in feature_cols]
                sequence_features.append(row_features)
            
            sequences.append(sequence_features)
            
            # 目标值
            target_row = df.iloc[i]
            
            targets['red_odd_even'].append(target_row['红球奇数个数'])
            targets['red_size'].append(target_row['红球大数个数'])
            targets['blue_size'].append(target_row['蓝球大数个数'])
            
            # 号码目标
            red_balls = [target_row[f'红球{k}'] for k in range(1, 6)]
            blue_balls = [target_row[f'蓝球{k}'] for k in range(1, 3)]
            
            red_target = np.zeros(35)
            blue_target = np.zeros(12)
            
            for ball in red_balls:
                if 1 <= ball <= 35:
                    red_target[int(ball) - 1] = 1
            for ball in blue_balls:
                if 1 <= ball <= 12:
                    blue_target[int(ball) - 1] = 1
            
            targets['red_numbers'].append(red_target)
            targets['blue_numbers'].append(blue_target)
        
        X = np.array(sequences, dtype=np.float32)
        
        # 特征标准化
        scaler = StandardScaler()
        X_reshaped = X.reshape(-1, X.shape[-1])
        X_scaled = scaler.fit_transform(X_reshaped)
        X = X_scaled.reshape(X.shape)
        
        # 转换目标
        y = {}
        for key in targets:
            y[key] = np.array(targets[key])
        
        return X, y, scaler
    
    def train_improved_model(self) -> Dict[str, Any]:
        """训练改进的模型"""
        print("V4.0 快速优化训练系统")
        print("=" * 50)
        
        if not TF_AVAILABLE:
            return {"success": False, "error": "TensorFlow不可用"}
        
        try:
            # 1. 数据准备
            print("\n[STEP 1] 数据准备")
            df = self.load_and_prepare_data()
            X, y, scaler = self.prepare_training_data(df)
            
            print(f"[INFO] 训练数据形状: {X.shape}")
            
            # 2. 数据分割
            print("\n[STEP 2] 数据分割")
            split_idx = int(len(X) * 0.8)
            X_train, X_val = X[:split_idx], X[split_idx:]
            y_train = {k: v[:split_idx] for k, v in y.items()}
            y_val = {k: v[split_idx:] for k, v in y.items()}
            
            print(f"[INFO] 训练集: {len(X_train)}, 验证集: {len(X_val)}")
            
            # 3. 创建改进模型
            print("\n[STEP 3] 创建改进模型")
            model = self.create_improved_model()
            print(f"[INFO] 模型参数数量: {model.count_params():,}")
            
            # 4. 训练模型
            print("\n[STEP 4] 开始训练")
            callbacks = [
                keras.callbacks.EarlyStopping(
                    monitor='val_loss', patience=8, restore_best_weights=True
                ),
                keras.callbacks.ReduceLROnPlateau(
                    monitor='val_loss', factor=0.5, patience=4, min_lr=1e-6
                )
            ]
            
            start_time = time.time()
            history = model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=50,
                batch_size=32,
                callbacks=callbacks,
                verbose=1
            )
            training_time = time.time() - start_time
            
            # 5. 保存模型
            print("\n[STEP 5] 保存模型")
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            model_path = self.models_dir / f"v4_improved_{timestamp}.h5"
            scaler_path = self.models_dir / f"scaler_improved_{timestamp}.pkl"
            
            model.save(str(model_path))
            with open(scaler_path, 'wb') as f:
                pickle.dump(scaler, f)
            
            print(f"[OK] 模型已保存: {model_path}")
            print(f"[OK] 标准化器已保存: {scaler_path}")
            
            # 6. 训练结果
            best_val_loss = min(history.history['val_loss'])
            best_epoch = history.history['val_loss'].index(best_val_loss) + 1
            
            result = {
                'success': True,
                'model_path': str(model_path),
                'scaler_path': str(scaler_path),
                'training_time': training_time,
                'best_val_loss': best_val_loss,
                'best_epoch': best_epoch,
                'total_epochs': len(history.history['val_loss'])
            }
            
            print(f"\n[SUCCESS] 训练完成!")
            print(f"最佳验证损失: {best_val_loss:.4f} (第{best_epoch}轮)")
            print(f"训练时间: {training_time:.1f}秒")
            
            return result
            
        except Exception as e:
            print(f"\n[ERROR] 训练失败: {e}")
            import traceback
            traceback.print_exc()
            return {"success": False, "error": str(e)}


def main():
    """主函数"""
    optimizer = V4QuickOptimizer()
    results = optimizer.train_improved_model()
    
    return results.get("success", False)


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
